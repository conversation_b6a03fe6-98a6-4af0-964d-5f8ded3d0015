# 文章詞彙圖像生成器

一個基於網頁的應用程式，可以將英文文章中的詞彙轉換為AI生成的圖像。

## 功能特色

- 📝 **文章處理**：智能分析英文文章，將詞彙轉換為可選取的元素
- 🎯 **詞彙選取**：點擊式選取機制，支援多詞彙選擇
- 🎨 **AI圖像生成**：使用Google Imagen 3.0 API生成高品質圖像
- 📊 **進度追蹤**：即時顯示生成進度和狀態
- 💾 **批次下載**：支援單張或批次下載生成的圖像
- 📱 **響應式設計**：支援桌面、平板和手機設備

## 使用方法

### 1. 準備API金鑰
- 前往 [Google Cloud Console](https://console.cloud.google.com/)
- 啟用 Imagen API 服務
- 創建API金鑰

### 2. 使用應用程式
1. **輸入文章**：在文字區域貼上英文文章
2. **處理文字**：點擊「處理文字」按鈕
3. **選取詞彙**：點擊文章中想要生成圖像的詞彙
4. **輸入API金鑰**：在指定欄位輸入您的Google API金鑰
5. **生成圖像**：點擊「生成圖像」按鈕開始處理
6. **下載結果**：可單張下載或批次下載所有圖像

## 技術規格

### 前端技術
- **HTML5** + **CSS3** + **JavaScript ES6+**
- **Tailwind CSS** - 快速樣式開發
- **Google Fonts** - Inter字體系列

### API服務
- **Google Imagen 3.0 API** - AI圖像生成
- **RESTful API** - 標準HTTP請求

### 瀏覽器支援
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 檔案結構

```
project/
├── index.html                           # 主要應用程式檔案
├── README.md                           # 專案說明文件
└── 文章詞彙圖像生成器開發指南.md        # 詳細開發文檔
```

## 安裝與部署

### 本地開發
1. 下載或克隆專案檔案
2. 在瀏覽器中開啟 `index.html`
3. 輸入您的Google API金鑰開始使用

### 網站部署
- 可部署到任何靜態網站託管服務
- 建議使用HTTPS（API要求）
- 支援GitHub Pages、Netlify、Vercel等平台

## 注意事項

### API使用
- 需要有效的Google Cloud API金鑰
- 請注意API使用配額和費用
- 建議設定適當的API限制

### 隱私安全
- API金鑰僅在瀏覽器本地使用
- 不會儲存或傳輸您的API金鑰
- 建議定期更換API金鑰

### 使用限制
- 目前僅支援英文文章處理
- 圖像生成速度取決於API回應時間
- 建議一次選取不超過10個詞彙

## 故障排除

### 常見問題

**Q: API金鑰無效錯誤**
A: 請確認：
- API金鑰格式正確
- 已啟用Imagen API服務
- API金鑰有足夠的配額

**Q: 圖像生成失敗**
A: 可能原因：
- 網路連線問題
- API服務暫時不可用
- 提示詞內容不符合API政策

**Q: 頁面載入緩慢**
A: 建議：
- 檢查網路連線
- 使用現代瀏覽器
- 清除瀏覽器快取

## 開發貢獻

歡迎提交問題報告和功能建議！

### 開發環境設定
1. 確保有現代瀏覽器和文字編輯器
2. 參考 `文章詞彙圖像生成器開發指南.md` 了解詳細架構
3. 遵循現有的程式碼風格和結構

### 功能擴展建議
- 多語言支援
- 更多圖像風格選項
- 使用者帳戶系統
- 圖像編輯功能
- 社群分享功能

## 授權條款

本專案採用 MIT 授權條款。

## 聯絡資訊

如有問題或建議，歡迎聯絡開發團隊。

---

**享受創作的樂趣！** 🎨✨
