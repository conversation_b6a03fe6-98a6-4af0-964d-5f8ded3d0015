<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章詞彙圖像生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
        }
        /* 可點擊單字的樣式 */
        .word-span {
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            border-radius: 4px;
            padding: 1px 2px;
            margin: 0 1px;
            display: inline-block;
        }
        .word-span:hover {
            background-color: #dbeafe;
            color: #1e40af;
        }
        /* 被選中單字的樣式 */
        .word-span.selected {
            background-color: #2563eb;
            color: white;
            font-weight: 600;
        }
        /* 載入動畫樣式 */
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* 風格選擇按鈕樣式 */
        .prompt-style-btn {
            padding: 8px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background-color: white;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            text-align: center;
        }
        .prompt-style-btn:hover {
            border-color: #3b82f6;
            background-color: #f0f9ff;
        }
        .prompt-style-btn.active {
            border-color: #3b82f6;
            background-color: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8 max-w-5xl">
        <header class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold text-gray-900">文章詞彙圖像生成器</h1>
            <p class="mt-2 text-md sm:text-lg text-gray-600">貼上英文文章，選取多個關鍵詞彙，AI 將為每個詞彙生成一張專屬圖像。</p>
        </header>

        <main class="space-y-6">
            <!-- 步驟一：輸入文章 -->
            <div class="bg-white p-6 rounded-xl shadow-md">
                <h2 class="text-xl font-semibold mb-3 text-gray-700">步驟一：貼上您的英文文章</h2>
                <textarea 
                    id="text-input" 
                    class="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition" 
                    placeholder="在這裡貼上英文文章...">
                </textarea>
                <button 
                    id="process-btn" 
                    class="mt-4 w-full sm:w-auto bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition transform hover:scale-105">
                    處理文字
                </button>
            </div>

            <!-- 步驟二：選取詞彙 -->
            <div id="selection-area" class="bg-white p-6 rounded-xl shadow-md hidden">
                <h2 class="text-xl font-semibold mb-3 text-gray-700">步驟二：點擊下方文章中的單字來選取</h2>
                <div 
                    id="text-output" 
                    class="text-lg leading-relaxed border border-gray-200 p-4 rounded-lg bg-gray-50 h-64 overflow-y-auto">
                    <!-- 處理後的文字會顯示在這裡 -->
                </div>
            </div>

            <!-- 步驟三：生成圖像 -->
            <div id="generation-area" class="bg-white p-6 rounded-xl shadow-md hidden">
                <h2 class="text-xl font-semibold mb-3 text-gray-700">步驟三：生成圖像</h2>
                <p class="text-sm text-gray-500 mb-2">下方將顯示您選取的詞彙。選擇圖像風格後，點擊按鈕開始生成圖片。</p>
                <div id="prompt-display" class="w-full p-3 border border-gray-200 bg-gray-50 rounded-lg min-h-[48px] mb-4"></div>

                <!-- 圖像風格選擇器 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">選擇圖像風格：</label>
                    <div class="grid grid-cols-2 sm:grid-cols-4 gap-2">
                        <button class="prompt-style-btn active" data-style="watercolor">
                            <span class="block text-sm font-medium">水彩畫</span>
                            <span class="block text-xs text-gray-500">柔和藝術風格</span>
                        </button>
                        <button class="prompt-style-btn" data-style="realistic">
                            <span class="block text-sm font-medium">寫實風格</span>
                            <span class="block text-xs text-gray-500">攝影級品質</span>
                        </button>
                        <button class="prompt-style-btn" data-style="artistic">
                            <span class="block text-sm font-medium">藝術插畫</span>
                            <span class="block text-xs text-gray-500">現代藝術風格</span>
                        </button>
                        <button class="prompt-style-btn" data-style="minimalist">
                            <span class="block text-sm font-medium">極簡設計</span>
                            <span class="block text-xs text-gray-500">簡潔現代風格</span>
                        </button>
                    </div>
                </div>

                <button
                    id="generate-btn"
                    class="w-full sm:w-auto bg-green-600 text-white font-semibold py-2 px-6 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed">
                    生成圖像
                </button>
            </div>

            <!-- 圖像顯示區域 -->
            <div id="image-display-area" class="bg-white p-6 rounded-xl shadow-md hidden">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">生成結果</h2>
                <div id="loader" class="flex justify-center items-center hidden">
                    <div class="text-center">
                        <div class="loader mx-auto"></div>
                        <p id="progress-text" class="mt-3 text-gray-600">圖像生成中，請稍候...</p>
                    </div>
                </div>
                <div id="message-box" class="text-center text-red-600 font-medium hidden p-3 bg-red-100 rounded-lg"></div>
                <div id="image-results-container" class="hidden mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 生成的圖片將會被動態添加到這裡 -->
                </div>
            </div>
        </main>
    </div>

    <script>
        // DOM 元素獲取
        const textInput = document.getElementById('text-input');
        const processBtn = document.getElementById('process-btn');
        const selectionArea = document.getElementById('selection-area');
        const textOutput = document.getElementById('text-output');
        const generationArea = document.getElementById('generation-area');
        const promptDisplay = document.getElementById('prompt-display');
        // API 配置
        const API_CONFIG = {
            baseUrl: 'https://ka.bt6.top/v1',
            fallbackUrl: 'https://kjp.bt6.top/v1',
            model: 'gpt-4o', // 使用支援視覺的模型
            apiKey: 'sk-1MPNX4B3wbqKEcLMWVEGaTfa1GVWQIwbOsVtQrNQd5W2NfGf'
        };

        // Prompt 模板配置
        const PROMPT_TEMPLATES = {
            watercolor: "Create a beautiful watercolor painting of '{concept}' with the word '{word}' elegantly incorporated into the artwork. Style: soft watercolor, artistic, 16:9 aspect ratio.",
            realistic: "Create a photorealistic image depicting '{concept}' with the text '{word}' prominently displayed. Style: high quality photography, professional lighting, 16:9 aspect ratio.",
            artistic: "Create an artistic illustration of '{concept}' featuring the word '{word}' as part of the design. Style: modern art, vibrant colors, creative composition, 16:9 aspect ratio.",
            minimalist: "Create a clean, minimalist design representing '{concept}' with the word '{word}' as the focal point. Style: simple, elegant, modern design, 16:9 aspect ratio."
        };

        // 當前選擇的 prompt 樣式
        let currentPromptStyle = 'watercolor';
        const generateBtn = document.getElementById('generate-btn');
        const imageDisplayArea = document.getElementById('image-display-area');
        const loader = document.getElementById('loader');
        const progressText = document.getElementById('progress-text');
        const messageBox = document.getElementById('message-box');
        const imageResultsContainer = document.getElementById('image-results-container');

        // 全域變數
        const selectedItems = new Map();
        let generationQueue = [];
        let totalQueueSize = 0;

        // 文字處理功能
        processBtn.addEventListener('click', () => {
            const text = textInput.value.trim();
            if (!text) {
                showMessage('請先輸入文章內容。');
                return;
            }

            textOutput.innerHTML = ''; // 清除之前的內容
            selectedItems.clear(); // 清除選取狀態
            updatePromptDisplay();

            // 將文章分解為句子
            const sentences = text.match(/[^.!?]+[.!?]*\s*/g) || [text];

            sentences.forEach(sentence => {
                const sentenceText = sentence.trim();
                if (!sentenceText) return;

                // 將每個句子分解為詞彙和標點符號
                const words = sentenceText.split(/(\s+|[.,!?;:"])/);

                words.forEach(word => {
                    if (word.trim() === '') {
                        // 空白字符直接添加
                        textOutput.appendChild(document.createTextNode(word));
                    } else if (/^[a-zA-Z0-9]+$/.test(word)) {
                        // 英文詞彙創建可點擊的span
                        const span = document.createElement('span');
                        span.textContent = word;
                        span.classList.add('word-span');
                        span.dataset.sentence = sentenceText; // 儲存句子上下文
                        span.addEventListener('click', handleWordClick);
                        textOutput.appendChild(span);
                    } else {
                        // 標點符號直接添加
                        textOutput.appendChild(document.createTextNode(word));
                    }
                });
            });

            // 顯示選取區域和生成區域
            selectionArea.classList.remove('hidden');
            generationArea.classList.remove('hidden');
        });

        // 處理詞彙點擊事件
        function handleWordClick(event) {
            const wordSpan = event.target;
            const word = wordSpan.textContent;
            const sentence = wordSpan.dataset.sentence;

            wordSpan.classList.toggle('selected');

            if (wordSpan.classList.contains('selected')) {
                selectedItems.set(word, sentence); // 儲存詞彙和句子
            } else {
                selectedItems.delete(word);
            }
            updatePromptDisplay();
        }

        // 更新選取詞彙顯示
        function updatePromptDisplay() {
            promptDisplay.innerHTML = ''; // 清除
            selectedItems.forEach((sentence, word) => {
                const tag = document.createElement('span');
                tag.textContent = word;
                tag.className = 'inline-block bg-blue-100 text-blue-800 text-sm font-medium mr-2 mb-2 px-2.5 py-0.5 rounded';
                promptDisplay.appendChild(tag);
            });
        }

        // 顯示訊息
        function showMessage(msg) {
            messageBox.textContent = msg;
            messageBox.classList.remove('hidden');
            imageDisplayArea.classList.remove('hidden');
            imageResultsContainer.classList.add('hidden');
        }

        // API圖像生成功能
        async function generateImageForItem(item, useBackup = false) {
            const { word, sentence } = item;

            // 使用選擇的prompt模板創建SVG生成指令
            const svgPrompt = `Create an SVG code for a 16:9 aspect ratio image (800x450) that visually represents "${sentence}" with the word "${word}" prominently displayed. Style: ${getStyleDescription(currentPromptStyle)}.

Requirements:
1. Return ONLY the SVG code, no explanations
2. Use viewBox="0 0 800 450"
3. Include the word "${word}" as readable text
4. Use appropriate colors and shapes for the concept
5. Make it visually appealing and artistic

SVG code:`;

            console.log(`正在生成圖像，提示詞: ${svgPrompt}`);

            try {
                const baseUrl = useBackup ? API_CONFIG.fallbackUrl : API_CONFIG.baseUrl;
                const apiUrl = `${baseUrl}/chat/completions`;

                const payload = {
                    model: API_CONFIG.model,
                    messages: [
                        {
                            role: "user",
                            content: svgPrompt
                        }
                    ],
                    max_tokens: 2000,
                    temperature: 0.7
                };

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_CONFIG.apiKey}`
                    },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('API 驗證失敗。請檢查 API 金鑰設定。');
                    }
                    if (response.status === 429) {
                        throw new Error('API 請求頻率限制。請稍後再試。');
                    }
                    if (response.status >= 500 && !useBackup) {
                        console.log('主要 API 服務器錯誤，嘗試備用服務器...');
                        return await generateImageForItem(item, true);
                    }

                    let errorMessage = `HTTP 錯誤! 狀態碼: ${response.status}`;
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.error?.message || errorData.message || JSON.stringify(errorData);
                    } catch (e) {
                        console.error(`詞彙 "${word}" 的錯誤回應不是有效的 JSON。`);
                    }
                    throw new Error(errorMessage);
                }

                const result = await response.json();

                if (result.choices && result.choices.length > 0) {
                    const svgContent = result.choices[0].message.content;
                    const cleanedSvg = extractAndCleanSVG(svgContent);

                    if (cleanedSvg) {
                        // 將SVG轉換為data URL
                        const svgBlob = new Blob([cleanedSvg], { type: 'image/svg+xml' });
                        const imageUrl = URL.createObjectURL(svgBlob);
                        return { word, imageUrl, error: null, svgContent: cleanedSvg };
                    } else {
                        // 如果SVG提取失敗，創建備用圖像
                        const fallbackImage = createStyledImage(word, sentence, currentPromptStyle);
                        return { word, imageUrl: fallbackImage, error: null };
                    }
                } else {
                    throw new Error('API 回應中未找到有效的內容。');
                }
            } catch (error) {
                console.error(`處理詞彙 "${word}" 時發生錯誤:`, error.message);
                // 創建備用圖像
                const fallbackImage = createStyledImage(word, sentence, currentPromptStyle);
                return { word, imageUrl: fallbackImage, error: `API錯誤，使用備用圖像: ${error.message}` };
            }
        }

        // 獲取風格描述
        function getStyleDescription(style) {
            const descriptions = {
                watercolor: "soft watercolor painting with flowing colors and artistic brushstrokes",
                realistic: "photorealistic style with detailed textures and professional lighting",
                artistic: "modern artistic illustration with vibrant colors and creative composition",
                minimalist: "clean minimalist design with simple shapes and elegant typography"
            };
            return descriptions[style] || descriptions.watercolor;
        }

        // 提取和清理SVG代碼
        function extractAndCleanSVG(content) {
            try {
                // 尋找SVG標籤
                const svgMatch = content.match(/<svg[\s\S]*?<\/svg>/i);
                if (svgMatch) {
                    let svg = svgMatch[0];
                    // 確保有正確的viewBox
                    if (!svg.includes('viewBox')) {
                        svg = svg.replace('<svg', '<svg viewBox="0 0 800 450"');
                    }
                    return svg;
                }
                return null;
            } catch (error) {
                console.error('SVG提取錯誤:', error);
                return null;
            }
        }

        // 創建風格化圖像（備用方案）
        function createStyledImage(word, sentence, style) {
            const canvas = document.createElement('canvas');
            canvas.width = 800;
            canvas.height = 450;
            const ctx = canvas.getContext('2d');

            // 根據風格設置顏色方案
            const styleConfig = {
                watercolor: {
                    bg: ['#f0f9ff', '#e0e7ff', '#dbeafe'],
                    text: '#1e40af',
                    accent: '#3b82f6'
                },
                realistic: {
                    bg: ['#f8fafc', '#f1f5f9', '#e2e8f0'],
                    text: '#0f172a',
                    accent: '#475569'
                },
                artistic: {
                    bg: ['#fef3c7', '#fed7aa', '#fbbf24'],
                    text: '#92400e',
                    accent: '#f59e0b'
                },
                minimalist: {
                    bg: ['#ffffff', '#f9fafb', '#f3f4f6'],
                    text: '#111827',
                    accent: '#6b7280'
                }
            };

            const config = styleConfig[style] || styleConfig.watercolor;

            // 創建漸變背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 450);
            gradient.addColorStop(0, config.bg[0]);
            gradient.addColorStop(0.5, config.bg[1]);
            gradient.addColorStop(1, config.bg[2]);
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 450);

            // 繪製裝飾性形狀
            ctx.fillStyle = config.accent + '20';
            for (let i = 0; i < 5; i++) {
                ctx.beginPath();
                ctx.arc(Math.random() * 800, Math.random() * 450, Math.random() * 100 + 20, 0, Math.PI * 2);
                ctx.fill();
            }

            // 繪製主要詞彙
            ctx.fillStyle = config.text;
            ctx.font = 'bold 64px Inter, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(word.toUpperCase(), 400, 200);

            // 繪製句子（較小字體）
            ctx.fillStyle = config.accent;
            ctx.font = '20px Inter, sans-serif';
            const words = sentence.split(' ');
            let line = '';
            let y = 300;

            for (let i = 0; i < words.length; i++) {
                const testLine = line + words[i] + ' ';
                const metrics = ctx.measureText(testLine);

                if (metrics.width > 600 && i > 0) {
                    ctx.fillText(line.trim(), 400, y);
                    line = words[i] + ' ';
                    y += 30;
                } else {
                    line = testLine;
                }
            }
            ctx.fillText(line.trim(), 400, y);

            // 添加風格標識
            ctx.fillStyle = config.accent + '80';
            ctx.font = '14px Inter, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillText(`${style.charAt(0).toUpperCase() + style.slice(1)} Style`, 780, 430);

            return canvas.toDataURL('image/png');
        }



        // 佇列處理系統
        async function processQueue() {
            if (generationQueue.length === 0) {
                loader.classList.add('hidden');
                generateBtn.disabled = false; // 重新啟用按鈕
                progressText.textContent = "所有圖片生成完畢！";
                return;
            }

            const currentItem = generationQueue.shift(); // 取得下一個項目
            const currentIndex = totalQueueSize - generationQueue.length;
            progressText.textContent = `( ${currentIndex} / ${totalQueueSize} ) 正在生成 "${currentItem.word}"...`;

            const result = await generateImageForItem(currentItem);

            if (result.imageUrl) {
                // 成功生成圖像
                const imgContainer = createImageContainer(result);
                imageResultsContainer.appendChild(imgContainer);

                // 如果是第一張圖片，添加批次下載按鈕
                if (imageResultsContainer.children.length === 1) {
                    addBatchDownloadButton();
                }
            } else {
                // 生成失敗
                const errorContainer = createErrorContainer(result);
                imageResultsContainer.appendChild(errorContainer);
            }

            // 繼續處理下一個項目
            processQueue();
        }

        // 生成按鈕事件監聽器
        generateBtn.addEventListener('click', () => {
            if (selectedItems.size === 0) {
                showMessage('請先選取至少一個詞彙。');
                return;
            }

            // 將 Map 條目轉換為佇列物件陣列
            generationQueue = Array.from(selectedItems.entries()).map(([word, sentence]) => ({ word, sentence }));
            totalQueueSize = generationQueue.length;

            // 準備 UI
            imageDisplayArea.classList.remove('hidden');
            loader.classList.remove('hidden');
            imageResultsContainer.innerHTML = ''; // 清除舊圖像
            imageResultsContainer.classList.remove('hidden');
            messageBox.classList.add('hidden');
            generateBtn.disabled = true; // 生成期間禁用按鈕

            // 開始處理佇列
            processQueue();
        });

        // 結果展示系統優化
        function createImageContainer(result) {
            const imgContainer = document.createElement('div');
            imgContainer.className = 'flex flex-col items-center space-y-2 bg-white p-4 rounded-lg shadow-sm border border-gray-200';

            const img = document.createElement('img');
            img.src = result.imageUrl;
            img.alt = `為 "${result.word}" 生成的圖片`;
            img.className = "w-full h-auto object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300";

            // 添加圖像載入錯誤處理
            img.onerror = function() {
                this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY5NzM4MyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWcluWDj+i8ieWFpeWksei8sDwvdGV4dD48L3N2Zz4=';
            };

            const wordLabel = document.createElement('p');
            wordLabel.textContent = result.word;
            wordLabel.className = "text-sm font-semibold text-gray-700 bg-blue-50 px-3 py-1 rounded-full border border-blue-200";

            // 添加下載按鈕
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = '下載';
            downloadBtn.className = "text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded transition-colors duration-200";
            downloadBtn.onclick = () => downloadImage(result.imageUrl, result.word);

            imgContainer.appendChild(img);
            imgContainer.appendChild(wordLabel);
            imgContainer.appendChild(downloadBtn);

            return imgContainer;
        }

        function createErrorContainer(result) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'p-4 bg-red-50 text-red-700 rounded-lg text-center border border-red-200';
            errorDiv.innerHTML = `
                <div class="flex flex-col items-center space-y-2">
                    <svg class="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <strong class="font-semibold">"${result.word}"</strong>
                    <p class="text-sm">生成失敗: ${result.error}</p>
                </div>
            `;
            return errorDiv;
        }

        // 圖像下載功能
        function downloadImage(imageUrl, word) {
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = `${word}_generated_image.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 添加批次下載功能
        function addBatchDownloadButton() {
            const existingBtn = document.getElementById('batch-download-btn');
            if (existingBtn) return; // 避免重複添加

            const batchDownloadBtn = document.createElement('button');
            batchDownloadBtn.id = 'batch-download-btn';
            batchDownloadBtn.textContent = '批次下載所有圖片';
            batchDownloadBtn.className = "mt-4 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200";
            batchDownloadBtn.onclick = batchDownloadImages;

            imageDisplayArea.appendChild(batchDownloadBtn);
        }

        function batchDownloadImages() {
            const images = imageResultsContainer.querySelectorAll('img');
            images.forEach((img, index) => {
                if (img.src.startsWith('data:image/png;base64,')) {
                    const word = img.alt.match(/"(.+?)"/)?.[1] || `image_${index + 1}`;
                    setTimeout(() => downloadImage(img.src, word), index * 100); // 延遲下載避免瀏覽器阻擋
                }
            });
        }

        // 重置應用程式狀態
        function resetApplication() {
            textInput.value = '';
            textOutput.innerHTML = '';
            promptDisplay.innerHTML = '';
            imageResultsContainer.innerHTML = '';
            selectedItems.clear();
            generationQueue = [];
            totalQueueSize = 0;

            selectionArea.classList.add('hidden');
            generationArea.classList.add('hidden');
            imageDisplayArea.classList.add('hidden');
            loader.classList.add('hidden');
            messageBox.classList.add('hidden');

            generateBtn.disabled = false;

            // 移除批次下載按鈕
            const batchBtn = document.getElementById('batch-download-btn');
            if (batchBtn) batchBtn.remove();
        }

        // 添加重置按鈕到標題區域
        function addResetButton() {
            const header = document.querySelector('header');
            const resetBtn = document.createElement('button');
            resetBtn.textContent = '重新開始';
            resetBtn.className = "mt-4 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200";
            resetBtn.onclick = resetApplication;
            header.appendChild(resetBtn);
        }

        // 風格選擇器事件處理
        function initializeStyleSelector() {
            const styleButtons = document.querySelectorAll('.prompt-style-btn');

            styleButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // 移除所有按鈕的active類
                    styleButtons.forEach(btn => btn.classList.remove('active'));
                    // 為當前按鈕添加active類
                    button.classList.add('active');
                    // 更新當前風格
                    currentPromptStyle = button.dataset.style;
                    console.log(`已選擇圖像風格: ${currentPromptStyle}`);
                });
            });
        }

        // 初始化應用程式
        document.addEventListener('DOMContentLoaded', () => {
            addResetButton();
            initializeStyleSelector();
            console.log('文章詞彙圖像生成器已載入並初始化完成');
        });
    </script>
</body>
</html>
