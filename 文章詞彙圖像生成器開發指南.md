# 文章詞彙圖像生成器開發指南

## 專案概述

### 專案目標
開發一個基於網頁的應用程式，允許使用者：
1. 輸入英文文章
2. 選取文章中的關鍵詞彙
3. 使用AI圖像生成API為每個詞彙生成相應的圖像

### 核心功能
- **文章處理**：將文章分解為可選取的詞彙
- **詞彙選取**：互動式詞彙選擇機制
- **圖像生成**：基於詞彙和上下文生成圖像
- **結果展示**：以網格形式展示生成的圖像

## 技術架構

### 前端技術棧
- **HTML5**：語義化標記
- **CSS**：Tailwind CSS框架
- **JavaScript**：ES6+原生JavaScript
- **字體**：Google Fonts (Inter)

### API服務
- **圖像生成**：Google Imagen 3.0 API
- **請求方式**：RESTful API
- **認證**：API Key

### 資料結構
```javascript
// 主要資料結構
const selectedItems = new Map(); // 儲存詞彙與句子的對應關係
let generationQueue = []; // 圖像生成佇列
let totalQueueSize = 0; // 總任務數量
```

## 詳細功能規格

### 1. 文字處理模組

#### 功能描述
將使用者輸入的文章分解為句子和詞彙，建立可互動的文字介面。

#### 實作要點
```javascript
// 句子分割正則表達式
const sentences = text.match(/[^.!?]+[.!?]*\s*/g) || [text];

// 詞彙分割正則表達式
const words = sentenceText.split(/(\s+|[.,!?;:"])/);

// 詞彙驗證正則表達式
/^[a-zA-Z0-9]+$/.test(word)
```

#### 資料儲存
- 每個詞彙span元素包含`data-sentence`屬性
- 儲存完整句子上下文用於圖像生成

### 2. 詞彙選取系統

#### 互動機制
- **點擊選取**：單擊詞彙進行選取/取消選取
- **視覺回饋**：選中狀態的樣式變化
- **狀態管理**：使用Map資料結構管理選取狀態

#### CSS樣式規格
```css
.word-span {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border-radius: 4px;
    padding: 1px 2px;
    margin: 0 1px;
}

.word-span:hover {
    background-color: #dbeafe;
    color: #1e40af;
}

.word-span.selected {
    background-color: #2563eb;
    color: white;
    font-weight: 600;
}
```

### 3. 圖像生成模組

#### API規格
- **端點**：`https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict`
- **方法**：POST
- **認證**：URL參數中的API Key

#### 請求格式
```javascript
const payload = {
    instances: [{ prompt: prompt }],
    parameters: { sampleCount: 1 }
};
```

#### 提示詞模板
```javascript
const prompt = `image size 16:9. A watercolor sketch of '${escapedSentence}' with the word '${word}' as the only text.`;
```

#### 錯誤處理
- HTTP狀態碼檢查
- API回應驗證
- 使用者友善的錯誤訊息

### 4. 佇列處理系統

#### 序列處理機制
```javascript
async function processQueue() {
    // 逐一處理避免API限制
    // 顯示進度資訊
    // 處理成功/失敗狀態
}
```

#### 進度顯示
- 當前處理項目
- 完成進度 (x/total)
- 載入動畫

## UI/UX設計規範

### 佈局結構
1. **標題區域**：應用程式名稱和描述
2. **步驟一**：文章輸入區域
3. **步驟二**：詞彙選取區域
4. **步驟三**：圖像生成控制
5. **結果展示**：圖像網格顯示

### 響應式設計
- **手機**：單欄佈局
- **平板**：雙欄網格
- **桌面**：三欄網格

### 互動回饋
- **按鈕狀態**：正常/懸停/禁用
- **載入狀態**：旋轉動畫
- **錯誤提示**：紅色警告框

## 開發實作指南

### 檔案結構
```
project/
├── index.html          # 主要HTML檔案
├── styles/
│   └── custom.css      # 自定義樣式（如需要）
├── scripts/
│   └── main.js         # 主要JavaScript邏輯（如需要分離）
└── README.md           # 專案說明
```

### 開發步驟

#### 第一階段：基礎結構
1. 建立HTML骨架
2. 引入Tailwind CSS
3. 建立基本佈局

#### 第二階段：文字處理
1. 實作文章輸入功能
2. 建立文字分割邏輯
3. 生成可點擊的詞彙元素

#### 第三階段：選取機制
1. 實作點擊事件處理
2. 建立選取狀態管理
3. 更新UI顯示

#### 第四階段：API整合
1. 建立API請求函數
2. 實作錯誤處理
3. 建立佇列處理機制

#### 第五階段：結果展示
1. 建立圖像顯示組件
2. 實作進度顯示
3. 優化使用者體驗

### 關鍵程式碼片段

#### DOM元素獲取
```javascript
const textInput = document.getElementById('text-input');
const processBtn = document.getElementById('process-btn');
const selectionArea = document.getElementById('selection-area');
const textOutput = document.getElementById('text-output');
const generateBtn = document.getElementById('generate-btn');
```

#### 事件監聽器設定
```javascript
processBtn.addEventListener('click', processText);
generateBtn.addEventListener('click', startGeneration);
```

#### 非同步API呼叫
```javascript
async function generateImageForItem(item) {
    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });
        // 處理回應
    } catch (error) {
        // 錯誤處理
    }
}
```

## 配置需求

### API設定
- 需要有效的Google Cloud API金鑰
- 啟用Imagen API服務
- 設定適當的配額限制

### 環境需求
- 現代瀏覽器支援（Chrome 80+, Firefox 75+, Safari 13+）
- 網路連線（用於API呼叫）
- 支援ES6+語法

## 擴展功能建議

### 短期擴展
1. **多語言支援**：支援其他語言的文章處理
2. **圖像風格選擇**：提供不同的藝術風格選項
3. **批次下載**：允許一次下載所有生成的圖像

### 長期擴展
1. **使用者帳戶系統**：儲存歷史記錄
2. **圖像編輯功能**：基本的圖像調整工具
3. **社群分享**：分享生成的圖像到社交媒體

## 測試策略

### 功能測試
- 文章處理正確性
- 詞彙選取機制
- API呼叫成功率
- 錯誤處理機制

### 使用者體驗測試
- 響應式設計測試
- 載入時間測試
- 互動流暢度測試

### 效能測試
- 大文章處理效能
- 多詞彙同時生成
- 記憶體使用情況

## 部署指南

### 靜態網站部署
- 可部署到GitHub Pages
- 或任何靜態網站託管服務
- 需要HTTPS支援（API要求）

### 環境變數
- API金鑰應透過環境變數或配置檔案管理
- 避免在前端程式碼中硬編碼敏感資訊

這份文檔提供了完整的開發指南，AI開發者可以基於此文檔實作完整的文章詞彙圖像生成器應用程式。
