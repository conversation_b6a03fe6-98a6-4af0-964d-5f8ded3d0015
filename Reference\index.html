<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>從文章詞彙生成圖像</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
        }
        /* 可點擊單字的樣式 */
        .word-span {
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            border-radius: 4px;
            padding: 1px 2px;
            margin: 0 1px;
            display: inline-block; /* 確保邊距和內距正確應用 */
        }
        .word-span:hover {
            background-color: #dbeafe; /* 淺藍色背景 */
            color: #1e40af; /* 深藍色文字 */
        }
        /* 被選中單字的樣式 */
        .word-span.selected {
            background-color: #2563eb; /* 藍色背景 */
            color: white; /* 白色文字 */
            font-weight: 600;
        }
        /* 載入動畫樣式 */
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8 max-w-5xl">
        <header class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold text-gray-900">文章詞彙圖像生成器</h1>
            <p class="mt-2 text-md sm:text-lg text-gray-600">貼上英文文章，選取多個關鍵詞彙，AI 將為每個詞彙生成一張專屬圖像。</p>
        </header>

        <main class="space-y-6">
            <!-- 步驟一：輸入文章 -->
            <div class="bg-white p-6 rounded-xl shadow-md">
                <h2 class="text-xl font-semibold mb-3 text-gray-700">步驟一：貼上您的英文文章</h2>
                <textarea id="text-input" class="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition" placeholder="在這裡貼上英文文章..."></textarea>
                <button id="process-btn" class="mt-4 w-full sm:w-auto bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition transform hover:scale-105">
                    處理文字
                </button>
            </div>

            <!-- 步驟二：選取詞彙 -->
            <div id="selection-area" class="bg-white p-6 rounded-xl shadow-md hidden">
                <h2 class="text-xl font-semibold mb-3 text-gray-700">步驟二：點擊下方文章中的單字來選取</h2>
                <div id="text-output" class="text-lg leading-relaxed border border-gray-200 p-4 rounded-lg bg-gray-50 h-64 overflow-y-auto">
                    <!-- 處理後的文字會顯示在這裡 -->
                </div>
            </div>

            <!-- 步驟三：生成圖像 -->
            <div id="generation-area" class="bg-white p-6 rounded-xl shadow-md hidden">
                <h2 class="text-xl font-semibold mb-3 text-gray-700">步驟三：生成圖像</h2>
                <p class="text-sm text-gray-500 mb-2">下方將顯示您選取的詞彙。點擊按鈕後，將為每個詞彙逐一生成圖片。</p>
                <div id="prompt-display" class="w-full p-3 border border-gray-200 bg-gray-50 rounded-lg min-h-[48px]"></div>
                <button id="generate-btn" class="mt-4 w-full sm:w-auto bg-green-600 text-white font-semibold py-2 px-6 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed">
                    生成圖像
                </button>
            </div>

            <!-- 圖像顯示區域 -->
            <div id="image-display-area" class="bg-white p-6 rounded-xl shadow-md hidden">
                 <h2 class="text-xl font-semibold mb-4 text-gray-700">生成結果</h2>
                <div id="loader" class="flex justify-center items-center hidden">
                    <div class="text-center">
                        <div class="loader mx-auto"></div>
                        <p id="progress-text" class="mt-3 text-gray-600">圖像生成中，請稍候...</p>
                    </div>
                </div>
                <div id="message-box" class="text-center text-red-600 font-medium hidden p-3 bg-red-100 rounded-lg"></div>
                <div id="image-results-container" class="hidden mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 生成的圖片將會被動態添加到這裡 -->
                </div>
            </div>
        </main>
    </div>

    <script>
        // DOM 元素獲取
        const textInput = document.getElementById('text-input');
        const processBtn = document.getElementById('process-btn');
        const selectionArea = document.getElementById('selection-area');
        const textOutput = document.getElementById('text-output');
        const generationArea = document.getElementById('generation-area');
        const promptDisplay = document.getElementById('prompt-display');
        const generateBtn = document.getElementById('generate-btn');
        const imageDisplayArea = document.getElementById('image-display-area');
        const loader = document.getElementById('loader');
        const progressText = document.getElementById('progress-text');
        const messageBox = document.getElementById('message-box');
        const imageResultsContainer = document.getElementById('image-results-container');

        // **MODIFIED**: Use a Map to store both the word and its sentence context.
        // Key: word, Value: sentence
        const selectedItems = new Map();
        let generationQueue = [];
        let totalQueueSize = 0;

        /**
         * **MODIFIED**: Processes text into sentences and words, storing context.
         */
        processBtn.addEventListener('click', () => {
            const text = textInput.value.trim();
            if (!text) {
                showMessage('請先輸入文章內容。');
                return;
            }

            textOutput.innerHTML = ''; // Clear previous content
            selectedItems.clear(); // Clear selections
            updatePromptDisplay();

            // Split text into sentences. This regex tries to keep sentences together.
            const sentences = text.match(/[^.!?]+[.!?]*\s*/g) || [text];

            sentences.forEach(sentence => {
                const sentenceText = sentence.trim();
                if (!sentenceText) return;

                // Split each sentence into words and punctuation
                const words = sentenceText.split(/(\s+|[.,!?;:"])/);

                words.forEach(word => {
                    if (word.trim() === '') {
                        textOutput.appendChild(document.createTextNode(word));
                    } else if (/^[a-zA-Z0-9]+$/.test(word)) {
                        const span = document.createElement('span');
                        span.textContent = word;
                        span.classList.add('word-span');
                        // **NEW**: Store the sentence context in a data attribute
                        span.dataset.sentence = sentenceText;
                        span.addEventListener('click', handleWordClick);
                        textOutput.appendChild(span);
                    } else {
                        textOutput.appendChild(document.createTextNode(word));
                    }
                });
            });

            selectionArea.classList.remove('hidden');
            generationArea.classList.remove('hidden');
        });

        /**
         * **MODIFIED**: Handles word clicks to store both word and sentence.
         */
        function handleWordClick(event) {
            const wordSpan = event.target;
            const word = wordSpan.textContent;
            const sentence = wordSpan.dataset.sentence; // Get sentence from data attribute

            wordSpan.classList.toggle('selected');

            if (wordSpan.classList.contains('selected')) {
                selectedItems.set(word, sentence); // Store word and its sentence
            } else {
                selectedItems.delete(word);
            }
            updatePromptDisplay();
        }

        /**
         * **MODIFIED**: Updates the display based on the keys of the selectedItems Map.
         */
        function updatePromptDisplay() {
            promptDisplay.innerHTML = ''; // Clear
            selectedItems.forEach((sentence, word) => {
                const tag = document.createElement('span');
                tag.textContent = word;
                tag.className = 'inline-block bg-blue-100 text-blue-800 text-sm font-medium mr-2 mb-2 px-2.5 py-0.5 rounded';
                promptDisplay.appendChild(tag);
            });
        }
        
        function showMessage(msg) {
            messageBox.textContent = msg;
            messageBox.classList.remove('hidden');
            imageDisplayArea.classList.remove('hidden');
            imageResultsContainer.classList.add('hidden');
        }

        /**
         * **MODIFIED**: Generates an image using the new prompt template.
         * @param {object} item - An object containing { word, sentence }.
         */
        async function generateImageForItem(item) {
            const { word, sentence } = item;
            // **NEW**: Construct the prompt using the specified template.
            // Escapes single quotes in the sentence to prevent breaking the prompt string.
            const escapedSentence = sentence.replace(/'/g, "\\'");
            const prompt = `image size 16:9. A watercolor sketch of '${escapedSentence}' with the word '${word}' as the only text.`;
            
            console.log(`Generating with prompt: ${prompt}`); // For debugging

            try {
                const payload = {
                    instances: [{ prompt: prompt }],
                    parameters: { sampleCount: 1 }
                };
                const apiKey = ""; // API key is provided by the environment
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=${apiKey}`;

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    if (response.status === 401) {
                        throw new Error('驗證失敗 (401)。請確認您的 API 金鑰是否有效且已啟用 Imagen API。');
                    }
                    let errorMessage = `HTTP 錯誤! 狀態碼: ${response.status}`;
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.error?.message || JSON.stringify(errorData);
                    } catch (e) {
                        console.error(`詞彙 "${word}" 的錯誤回應不是有效的 JSON。`);
                    }
                    throw new Error(errorMessage);
                }

                const result = await response.json();

                if (result.predictions && result.predictions.length > 0 && result.predictions[0].bytesBase64Encoded) {
                    const base64Data = result.predictions[0].bytesBase64Encoded;
                    const imageUrl = `data:image/png;base64,${base64Data}`;
                    return { word, imageUrl, error: null };
                } else {
                    throw new Error('API 回應中未找到有效的圖像資料。');
                }
            } catch (error) {
                console.error(`處理詞彙 "${word}" 時發生錯誤:`, error.message);
                return { word, imageUrl: null, error: error.message };
            }
        }

        /**
         * Sequentially process the generation queue.
         */
        async function processQueue() {
            if (generationQueue.length === 0) {
                loader.classList.add('hidden');
                generateBtn.disabled = false; // Re-enable button
                progressText.textContent = "所有圖片生成完畢！";
                return;
            }

            const currentItem = generationQueue.shift(); // Get the next item {word, sentence}
            const currentIndex = totalQueueSize - generationQueue.length;
            progressText.textContent = `( ${currentIndex} / ${totalQueueSize} ) 正在生成 "${currentItem.word}"...`;

            const result = await generateImageForItem(currentItem);

            if (result.imageUrl) {
                const imgContainer = document.createElement('div');
                imgContainer.className = 'flex flex-col items-center space-y-2';
                const img = document.createElement('img');
                img.src = result.imageUrl;
                img.alt = `為 "${result.word}" 生成的圖片`;
                img.className = "w-full h-auto object-cover rounded-lg shadow-lg";
                const p = document.createElement('p');
                p.textContent = result.word;
                p.className = "text-sm font-semibold text-gray-700 bg-gray-100 px-3 py-1 rounded-full";
                imgContainer.appendChild(img);
                imgContainer.appendChild(p);
                imageResultsContainer.appendChild(imgContainer);
            } else {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'p-3 bg-red-100 text-red-700 rounded-lg text-center';
                errorDiv.innerHTML = `<strong>"${result.word}"</strong><br>生成失敗: ${result.error}`;
                imageResultsContainer.appendChild(errorDiv);
            }
            
            processQueue();
        }


        /**
         * **MODIFIED**: Main button listener to set up the queue with {word, sentence} objects.
         */
        generateBtn.addEventListener('click', () => {
            if (selectedItems.size === 0) {
                showMessage('請先選取至少一個詞彙。');
                return;
            }

            // Convert Map entries to an array of objects for the queue
            generationQueue = Array.from(selectedItems.entries()).map(([word, sentence]) => ({ word, sentence }));
            totalQueueSize = generationQueue.length;

            // Prepare UI
            imageDisplayArea.classList.remove('hidden');
            loader.classList.remove('hidden');
            imageResultsContainer.innerHTML = ''; // Clear old images
            imageResultsContainer.classList.remove('hidden');
            messageBox.classList.add('hidden');
            generateBtn.disabled = true; // Disable button during generation

            // Start processing the queue
            processQueue();
        });

    </script>
</body>
</html>
